spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************
    username: root
    password: zhyzhy356789

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      # password:
      database: 0
      timeout: 5000ms
      # 连接池配置
      lettuce:
        pool:
          # 连接池最大连接数
          max-active: 20
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
          # 连接池中的最大空闲连接
          max-idle: 10
          # 连接池中的最小空闲连接
          min-idle: 2

    # 集群配置（如果使用集群模式）
    # cluster:
    #   nodes:
    #     - 127.0.0.1:7001
    #     - 127.0.0.1:7002
    #     - 127.0.0.1:7003
    #   max-redirects: 3
    # 哨兵配置（如果使用哨兵模式）
    # sentinel:
    #   master: mymaster
    #   nodes:
    #     - 127.0.0.1:26379
    #     - 127.0.0.1:26380
    #     - 127.0.0.1:26381

  # RabbitMQ配置
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
    # 连接超时时间
    connection-timeout: 15000
    # 发布确认
    publisher-confirm-type: correlated
    # 发布返回
    publisher-returns: true
    # 消费者配置
    listener:
      simple:
        # 手动确认消息
        acknowledge-mode: manual
        # 并发消费者数量
        concurrency: 1
        # 最大并发消费者数量
        max-concurrency: 10
        # 预取数量
        prefetch: 1
        # 重试配置
        retry:
          enabled: true
          initial-interval: 1000
          max-attempts: 3
          max-interval: 10000
          multiplier: 1.0

# MyBatis Plus 配置
mybatis-plus:
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启 SQL 日志
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # Mapper XML 文件位置
  mapper-locations: classpath*:/mapper/**/*.xml
  # 实体类别名包路径
  type-aliases-package: com.subfg.domain.entity

logging:
  level:
    "[com.subfg.repository.mapper]": DEBUG
