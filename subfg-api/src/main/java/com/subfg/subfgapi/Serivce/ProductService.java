package com.subfg.subfgapi.Serivce;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.subfg.domain.entity.product.ProductPo;
import com.subfg.domain.request.ProductPageReq;
import com.subfg.repository.mapper.ProductMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 产品服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductService {

    private final ProductMapper productMapper;

    /**
     * 分页获取产品列表
     */
    public Page<ProductPo> page(ProductPageReq req) {
        log.info("开始查询产品分页列表，请求参数：{}", req);

        // 构建查询条件
        LambdaQueryWrapper<ProductPo> queryWrapper = new LambdaQueryWrapper<>();

        // 产品名称关键字模糊搜索
        if (StringUtils.hasText(req.getKeyword())) {
            queryWrapper.like(ProductPo::getName, req.getKeyword().trim());
        }

        // 公司名称
        if (StringUtils.hasText(req.getCompany())) {
            queryWrapper.eq(ProductPo::getCompany, req.getCompany().trim());
        }

        // 产品分类
        if (req.getCategory() != null) {
            queryWrapper.eq(ProductPo::getCategory, req.getCategory());
        }

        // 按ID倒序排列
        queryWrapper.orderByDesc(ProductPo::getId);
        queryWrapper.eq(ProductPo::getEnable, true);

        // 分页查询
        Page<ProductPo> page = new Page<>(req.getPageNum(), req.getPageSize());
        Page<ProductPo> result = productMapper.selectPage(page, queryWrapper);

        log.info("产品分页列表查询完成，共查询到{}条记录", result.getRecords().size());
        return result;
    }

}
