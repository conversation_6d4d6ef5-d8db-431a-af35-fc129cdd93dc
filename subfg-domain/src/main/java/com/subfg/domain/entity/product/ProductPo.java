package com.subfg.domain.entity.product;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 产品实体类
 * 对应数据库表：product
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("product")
public class ProductPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 产品名称
     */
    @TableField("name")
    private String name;

    /**
     * 公司
     */
    @TableField("company")
    private String company;

    /**
     * 分类
     */
    @TableField("category")
    private Integer category;

    /**
     * 图标URL
     */
    @TableField("icon_url")
    private String iconUrl;

    /**
     * 产品URL
     */
    @TableField("product_url")
    private String productUrl;

    /**
     * 原价
     */
    @TableField("original_price")
    private BigDecimal originalPrice;

    /**
     * 平均价格
     */
    @TableField("average_price")
    private BigDecimal averagePrice;

    /**
     * 等级
     */
    @TableField("grade")
    private Integer grade;

    /**
     * 热门度
     */
    @TableField("popular")
    private Integer popular;

    /**
     * 是否在商店
     */
    @TableField("is_store")
    private Boolean isStore;

    /**
     * 分享数量
     */
    @TableField("share_count")
    private Integer shareCount;

    /**
     * 创建者
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新者
     */
    @TableField("updator")
    private String updator;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Long deleteTime;

    /**
     * 删除者
     */
    @TableField("delete")
    private String delete;

    /**
     * 是否启用
     */
    @TableField("enable")
    private Boolean enable;
}
