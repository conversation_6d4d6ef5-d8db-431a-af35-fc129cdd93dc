package com.subfg.domain.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

/**
 * 产品分页查询请求参数
 */
@Data
public class ProductPageReq {

    /**
     * 页码（从1开始）
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer pageSize = 10;

    /**
     * 产品名称关键字（模糊搜索）
     */
    private String keyword;

    /**
     * 公司名称
     */
    private String company;

    /**
     * 产品分类ID
     */
    private Integer category;

}
